import os
import sys

from alembic import context
from dotenv import load_dotenv
from sqlalchemy import create_engine, pool

# Get project root path (one level up from alembic directory)
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Add project root to path before loading environment variables
sys.path.append(project_root)

# Load environment variables
env_path = os.path.join(project_root, ".env")
load_dotenv(env_path)

# Import from modules after path setup
from common.db.database import Base
from modules.auth.infrastructure.oauth_provider_orm import (  # noqa: F401
    OAuthProviderORM,
)
from modules.auth.infrastructure.orm import OAuthAccountORM  # noqa: F401

# Import all ORM models to ensure they are registered with Base
from modules.user.infrastructure.user_orm import UserORM  # noqa: F401

# Get database URL from environment
db_url = os.getenv("AI4SE_MCP_HUB_DB_URL")
if not db_url:
    raise ValueError("Environment variable AI4SE_MCP_HUB_DB_URL is not set")

# Set target metadata
target_metadata = Base.metadata


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = create_engine(db_url, poolclass=pool.NullPool)

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            render_as_batch=True,
        )

        with context.begin_transaction():
            context.run_migrations()


run_migrations_online()
