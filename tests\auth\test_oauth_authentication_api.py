"""Tests for OAuth authentication API endpoints."""

import uuid
from unittest.mock import AsyncMock, patch

from fastapi.testclient import Test<PERSON>lient

from modules.auth.domain.oauth_account_models import OAuthAccount
from modules.user.domain.user_models import User, UserStatus


class TestOAuthAuthenticationAPI:
    """Test cases for OAuth authentication API."""

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.get_available_providers"
    )
    def should_return_available_oauth_providers_when_requested(
        self, mock_get_providers: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that available OAuth providers are returned."""
        mock_get_providers.return_value = [
            {
                "name": "github",
                "display_name": "GitHub",
                "description": "GitHub OAuth provider",
                "icon_url": "https://github.com/favicon.ico",
            },
            {
                "name": "google",
                "display_name": "Google",
                "description": "Google OAuth provider",
                "icon_url": "https://google.com/favicon.ico",
            },
        ]

        response = test_client.get("/api/v1/auth/oauth/providers")

        assert response.status_code == 200
        data = response.json()
        assert "providers" in data
        assert len(data["providers"]) == 2
        assert data["providers"][0]["name"] == "github"
        assert data["providers"][1]["name"] == "google"

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.get_authorization_url"
    )
    def should_generate_github_authorization_url_when_valid_provider_requested(
        self, mock_get_auth_url: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that GitHub authorization URL is generated successfully."""
        mock_get_auth_url.return_value = "https://github.com/login/oauth/authorize?client_id=test&redirect_uri=callback&scope=user:email&response_type=code"

        response = test_client.get("/api/v1/auth/oauth/github/authorize")

        assert response.status_code == 200
        data = response.json()
        assert "authorization_url" in data
        assert "https://github.com/login/oauth/authorize" in data["authorization_url"]

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.get_authorization_url"
    )
    def should_include_state_parameter_when_state_provided_in_authorization_url(
        self, mock_get_auth_url: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that state parameter is included in authorization URL when provided."""
        state = "test_state_123"
        mock_get_auth_url.return_value = f"https://github.com/login/oauth/authorize?client_id=test&redirect_uri=callback&scope=user:email&response_type=code&state={state}"

        response = test_client.get(f"/api/v1/auth/oauth/github/authorize?state={state}")

        assert response.status_code == 200
        data = response.json()
        assert data["state"] == state
        assert f"state={state}" in data["authorization_url"]

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.get_authorization_url"
    )
    def should_reject_invalid_oauth_provider_when_unsupported_provider_requested(
        self, mock_get_auth_url: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that invalid OAuth provider is rejected."""
        mock_get_auth_url.side_effect = ValueError("OAuth provider 'invalid' not found")

        response = test_client.get("/api/v1/auth/oauth/invalid/authorize")

        assert response.status_code == 404
        assert "not found" in response.json()["detail"]

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.handle_oauth_callback"
    )
    def should_handle_github_oauth_callback_when_valid_code_provided(
        self, mock_handle_callback: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that GitHub OAuth callback is handled successfully."""
        # Mock user and token
        mock_user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed",
            status=UserStatus.ACTIVE,
        )
        mock_handle_callback.return_value = (mock_user, "jwt_access_token")

        response = test_client.post(
            "/api/v1/auth/oauth/github/callback",
            json={"code": "test_authorization_code"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["username"] == "testuser"

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.handle_oauth_callback"
    )
    def should_reject_oauth_callback_when_invalid_code_provided(
        self, mock_handle_callback: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that OAuth callback is rejected when invalid code is provided."""
        mock_handle_callback.side_effect = ValueError("Invalid authorization code")

        response = test_client.post(
            "/api/v1/auth/oauth/github/callback",
            json={"code": "invalid_code"},
        )

        assert response.status_code == 400
        assert "Invalid authorization code" in response.json()["detail"]

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.get_user_oauth_accounts"
    )
    def should_return_user_oauth_accounts_when_authenticated_user_requests(
        self, mock_get_accounts: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that user's OAuth accounts are returned for authenticated user."""
        from modules.auth.interfaces.dependencies import get_current_user

        # Mock OAuth accounts
        mock_accounts = [
            OAuthAccount(
                id=uuid.uuid4(),
                user_id=uuid.uuid4(),
                provider="github",
                provider_account_id="12345",
            ),
            OAuthAccount(
                id=uuid.uuid4(),
                user_id=uuid.uuid4(),
                provider="google",
                provider_account_id="67890",
            ),
        ]
        mock_get_accounts.return_value = mock_accounts

        # Create mock user
        mock_user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed",
            status=UserStatus.ACTIVE,
        )

        # Override dependency
        def mock_get_current_user():
            return mock_user

        test_client.app.dependency_overrides[get_current_user] = mock_get_current_user

        try:
            response = test_client.get(
                "/api/v1/auth/oauth/accounts",
                headers={"Authorization": "Bearer valid_token"},
            )

            assert response.status_code == 200
            data = response.json()
            assert "oauth_accounts" in data
            assert len(data["oauth_accounts"]) == 2
            assert data["oauth_accounts"][0]["provider"] == "github"
            assert data["oauth_accounts"][1]["provider"] == "google"
        finally:
            # Clean up dependency override
            test_client.app.dependency_overrides.clear()

    def should_deny_access_to_oauth_accounts_without_authentication(
        self, test_client: TestClient
    ) -> None:
        """Test that OAuth accounts endpoint denies access without authentication."""
        response = test_client.get("/api/v1/auth/oauth/accounts")

        assert response.status_code == 403

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.unlink_oauth_account"
    )
    def should_unlink_oauth_account_when_valid_account_id_provided(
        self, mock_unlink: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that OAuth account is unlinked successfully."""
        from modules.auth.interfaces.dependencies import get_current_user

        mock_unlink.return_value = True
        oauth_account_id = uuid.uuid4()

        # Create mock user
        mock_user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed",
            status=UserStatus.ACTIVE,
        )

        # Override dependency
        def mock_get_current_user():
            return mock_user

        test_client.app.dependency_overrides[get_current_user] = mock_get_current_user

        try:
            response = test_client.delete(
                f"/api/v1/auth/oauth/accounts/{oauth_account_id}",
                headers={"Authorization": "Bearer valid_token"},
            )

            assert response.status_code == 200
            assert response.json()["message"] == "OAuth account unlinked successfully"
        finally:
            # Clean up dependency override
            test_client.app.dependency_overrides.clear()

    @patch(
        "modules.auth.application.oauth_authentication_service.OAuthAuthenticationService.unlink_oauth_account"
    )
    def should_return_not_found_when_unlinking_nonexistent_oauth_account(
        self, mock_unlink: AsyncMock, test_client: TestClient
    ) -> None:
        """Test that 404 is returned when trying to unlink nonexistent OAuth account."""
        from modules.auth.interfaces.dependencies import get_current_user

        mock_unlink.return_value = False
        oauth_account_id = uuid.uuid4()

        # Create mock user
        mock_user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed",
            status=UserStatus.ACTIVE,
        )

        # Override dependency
        def mock_get_current_user():
            return mock_user

        test_client.app.dependency_overrides[get_current_user] = mock_get_current_user

        try:
            response = test_client.delete(
                f"/api/v1/auth/oauth/accounts/{oauth_account_id}",
                headers={"Authorization": "Bearer valid_token"},
            )

            assert response.status_code == 404
            assert "not found" in response.json()["detail"]
        finally:
            # Clean up dependency override
            test_client.app.dependency_overrides.clear()

    def should_deny_oauth_account_unlinking_without_authentication(
        self, test_client: TestClient
    ) -> None:
        """Test that OAuth account unlinking denies access without authentication."""
        oauth_account_id = uuid.uuid4()
        response = test_client.delete(f"/api/v1/auth/oauth/accounts/{oauth_account_id}")

        assert response.status_code == 403
