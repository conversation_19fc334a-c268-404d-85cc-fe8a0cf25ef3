{"data_mtime": **********, "dep_lines": [18, 19, 25, 26, 1, 6, 7, 17, 24, 27, 28, 29, 30, 36, 37, 38, 1, 2, 3, 4, 6, 35, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 5, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.dependencies.utils", "fastapi.openapi.constants", "fastapi.openapi.models", "http.client", "fastapi.routing", "fastapi._compat", "fastapi.datastructures", "fastapi.encoders", "fastapi.params", "fastapi.responses", "fastapi.types", "fastapi.utils", "starlette.responses", "starlette.routing", "starlette.status", "http", "inspect", "warnings", "typing", "<PERSON><PERSON><PERSON>", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "enum", "fastapi.dependencies", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.json_schema", "pydantic.main", "starlette"], "hash": "9573a063e65be9624ac53e77267028d58f44b4da", "id": "fastapi.openapi.utils", "ignore_all": true, "interface_hash": "fa5f195f11e25162e452fa756d4e1759378a617f", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "D:\\Workspace\\thoughtworks\\ai4se-workspace\\ai4se-mcp-hub\\.venv\\Lib\\site-packages\\fastapi\\openapi\\utils.py", "plugin_data": null, "size": 23964, "suppressed": [], "version_id": "1.16.1"}