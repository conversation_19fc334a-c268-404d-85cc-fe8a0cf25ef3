{"data_mtime": 1750763435, "dep_lines": [5, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.testclient", "uuid", "builtins", "_collections_abc", "_frozen_importlib", "abc", "http", "http.cookiejar", "httpx", "httpx._auth", "httpx._client", "httpx._config", "httpx._models", "httpx._urls", "starlette", "starlette.testclient", "typing"], "hash": "816e03377c958a71d18ae928af394858ec8af402", "id": "tests.auth.test_credential_auth_api", "ignore_all": false, "interface_hash": "3e300a40eeb4b6232f5454975948e06b31ebdf2e", "mtime": 1750767377, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "tests\\auth\\test_credential_auth_api.py", "plugin_data": null, "size": 11347, "suppressed": [], "version_id": "1.16.1"}