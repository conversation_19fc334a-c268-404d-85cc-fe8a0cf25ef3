"""OAuth authentication API endpoints."""

import uuid

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Query, status
from fastapi.responses import HTMLResponse

from modules.auth.application.oauth_authentication_service import (
    OAuthAuthenticationService,
)
from modules.auth.interfaces.credential_auth_schemas import LoginResponse, UserResponse
from modules.auth.interfaces.dependencies import (
    get_current_user,
    get_oauth_authentication_service,
)
from modules.auth.interfaces.oauth_authentication_schemas import (
    OAuthAccountResponse,
    OAuthAuthorizationResponse,
    OAuthCallbackRequest,
    OAuthProviderInfo,
    OAuthProvidersResponse,
    UserOAuthAccountsResponse,
)
from modules.user.domain.user_models import User

router = APIRouter(prefix="/auth/oauth", tags=["OAuth Authentication"])


@router.get(
    "/providers",
    response_model=OAuthProvidersResponse,
    summary="Get available OAuth providers",
    description="Get list of all available OAuth providers for authentication",
)
async def get_oauth_providers(
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> OAuthProvidersResponse:
    """Get list of available OAuth providers."""
    providers_data = await oauth_service.get_available_providers()

    providers = [
        OAuthProviderInfo(
            name=provider["name"],
            display_name=provider["display_name"],
            description=provider["description"],
            icon_url=provider["icon_url"],
        )
        for provider in providers_data
    ]

    return OAuthProvidersResponse(providers=providers)


@router.get(
    "/{provider}/authorize",
    response_model=OAuthAuthorizationResponse,
    summary="Get OAuth authorization URL",
    description="Generate OAuth authorization URL for the specified provider",
)
async def get_oauth_authorization_url(
    provider: str,
    state: str | None = Query(None, description="State parameter for CSRF protection"),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> OAuthAuthorizationResponse:
    """Generate OAuth authorization URL for the specified provider."""
    try:
        authorization_url = await oauth_service.get_authorization_url(provider, state)
        return OAuthAuthorizationResponse(
            authorization_url=authorization_url,
            state=state,
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        ) from e


@router.get(
    "/{provider}/callback",
    summary="Handle OAuth callback",
    description="Handle OAuth callback and authenticate user",
)
async def handle_oauth_callback(
    provider: str,
    code: str = Query(..., description="Authorization code from OAuth provider"),
    state: str | None = Query(None, description="State parameter for CSRF protection"),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
):
    """Handle OAuth callback and authenticate user."""
    try:
        user, access_token = await oauth_service.handle_oauth_callback(
            provider=provider,
            code=code,
        )

        # Return HTML page with token for easy testing
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>OAuth Login Success</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
                .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .success {{ color: #28a745; font-size: 24px; margin-bottom: 20px; }}
                .token-section {{ background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }}
                .token {{ font-family: monospace; word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px; }}
                .user-info {{ background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .copy-btn {{ background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }}
                .copy-btn:hover {{ background: #0056b3; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="success">✅ OAuth Login Successful!</div>

                <div class="user-info">
                    <h3>User Information:</h3>
                    <p><strong>ID:</strong> {user.id}</p>
                    <p><strong>Username:</strong> {user.username}</p>
                    <p><strong>Email:</strong> {user.email or 'Not provided'}</p>
                    <p><strong>Provider:</strong> {provider.title()}</p>
                    <p><strong>Status:</strong> {user.status.value}</p>
                </div>

                <div class="token-section">
                    <h3>Access Token:</h3>
                    <div class="token" id="token">{access_token}</div>
                    <button class="copy-btn" onclick="copyToken()">Copy Token</button>
                </div>

                <div style="margin-top: 30px;">
                    <h3>Test Protected Endpoints:</h3>
                    <p>Use the access token above to test protected endpoints:</p>
                    <ul>
                        <li><code>GET /api/v1/auth/credential/me</code> - Get current user info</li>
                        <li><code>GET /api/v1/auth/oauth/accounts</code> - Get OAuth accounts</li>
                    </ul>
                    <p>Add header: <code>Authorization: Bearer &lt;token&gt;</code></p>
                </div>
            </div>

            <script>
                function copyToken() {{
                    const token = document.getElementById('token').textContent;
                    navigator.clipboard.writeText(token).then(() => {{
                        alert('Token copied to clipboard!');
                    }});
                }}
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=html_content)

    except ValueError as e:
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>OAuth Login Error</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .error {{ color: #dc3545; font-size: 24px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="error">❌ OAuth Login Failed</div>
                <p><strong>Error:</strong> {str(e)}</p>
                <p>Please try again or contact support if the problem persists.</p>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=error_html, status_code=400)


@router.get(
    "/accounts",
    response_model=UserOAuthAccountsResponse,
    summary="Get user's OAuth accounts",
    description="Get all OAuth accounts linked to the current user",
)
async def get_user_oauth_accounts(
    current_user: User = Depends(get_current_user),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> UserOAuthAccountsResponse:
    """Get all OAuth accounts linked to the current user."""
    oauth_accounts = oauth_service.get_user_oauth_accounts(current_user.id)

    accounts_response = [
        OAuthAccountResponse(
            id=account.id,
            provider=account.provider,
            provider_account_id=account.provider_account_id,
            created_at=account.created_at.isoformat(),
        )
        for account in oauth_accounts
    ]

    return UserOAuthAccountsResponse(oauth_accounts=accounts_response)


@router.delete(
    "/accounts/{oauth_account_id}",
    status_code=status.HTTP_200_OK,
    summary="Unlink OAuth account",
    description="Unlink an OAuth account from the current user",
)
async def unlink_oauth_account(
    oauth_account_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> dict:
    """Unlink an OAuth account from the current user."""
    try:
        success = oauth_service.unlink_oauth_account(
            user_id=current_user.id,
            oauth_account_id=oauth_account_id,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="OAuth account not found or does not belong to current user",
            )

        return {"message": "OAuth account unlinked successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
