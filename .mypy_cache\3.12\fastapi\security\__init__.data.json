{".class": "MypyFile", "_fullname": "fastapi.security", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIKeyCookie": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.api_key.APIKeyCookie", "kind": "Gdef"}, "APIKeyHeader": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.api_key.APIKeyHeader", "kind": "Gdef"}, "APIKeyQuery": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.api_key.APIKeyQuery", "kind": "Gdef"}, "HTTPAuthorizationCredentials": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.http.HTTPAuthorizationCredentials", "kind": "Gdef"}, "HTTPBasic": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.http.HTTPBasic", "kind": "Gdef"}, "HTTPBasicCredentials": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.http.HTTPBasicCredentials", "kind": "Gdef"}, "HTTPBearer": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.http.HTTPBearer", "kind": "Gdef"}, "HTTPDigest": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.http.HTTPDigest", "kind": "Gdef"}, "OAuth2": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.oauth2.OAuth2", "kind": "Gdef"}, "OAuth2AuthorizationCodeBearer": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.oauth2.OAuth2AuthorizationCodeBearer", "kind": "Gdef"}, "OAuth2PasswordBearer": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.oauth2.OAuth2PasswordBearer", "kind": "Gdef"}, "OAuth2PasswordRequestForm": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.oauth2.OAuth2PasswordRequestForm", "kind": "Gdef"}, "OAuth2PasswordRequestFormStrict": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.oauth2.OAuth2PasswordRequestFormStrict", "kind": "Gdef"}, "OpenIdConnect": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.open_id_connect_url.OpenIdConnect", "kind": "Gdef"}, "SecurityScopes": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.oauth2.SecurityScopes", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "D:\\Workspace\\thoughtworks\\ai4se-workspace\\ai4se-mcp-hub\\.venv\\Lib\\site-packages\\fastapi\\security\\__init__.py"}