#!/usr/bin/env python3
"""Test OAuth flow and protected endpoints."""

import json
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import requests


def test_oauth_endpoints():
    """Test OAuth endpoints and protected routes."""
    
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing OAuth endpoints...")
    
    # Test 1: Get OAuth providers
    print("\n1️⃣ Testing OAuth providers endpoint...")
    response = requests.get(f"{base_url}/api/v1/auth/oauth/providers")
    if response.status_code == 200:
        providers = response.json()
        print(f"✅ OAuth providers: {len(providers['providers'])} found")
        for provider in providers['providers']:
            print(f"   - {provider['display_name']} ({provider['name']})")
    else:
        print(f"❌ Failed to get OAuth providers: {response.status_code}")
        return False
    
    # Test 2: Get GitHub authorization URL
    print("\n2️⃣ Testing GitHub authorization URL...")
    response = requests.get(f"{base_url}/api/v1/auth/oauth/github/authorize")
    if response.status_code == 200:
        auth_data = response.json()
        print(f"✅ GitHub authorization URL generated")
        print(f"   URL: {auth_data['authorization_url'][:80]}...")
    else:
        print(f"❌ Failed to get GitHub authorization URL: {response.status_code}")
        return False
    
    # Test 3: Get Google authorization URL
    print("\n3️⃣ Testing Google authorization URL...")
    response = requests.get(f"{base_url}/api/v1/auth/oauth/google/authorize")
    if response.status_code == 200:
        auth_data = response.json()
        print(f"✅ Google authorization URL generated")
        print(f"   URL: {auth_data['authorization_url'][:80]}...")
    else:
        print(f"❌ Failed to get Google authorization URL: {response.status_code}")
        return False
    
    # Test 4: Test protected endpoint without token
    print("\n4️⃣ Testing protected endpoint without token...")
    response = requests.get(f"{base_url}/api/v1/auth/credential/me")
    if response.status_code == 403:
        print("✅ Protected endpoint correctly denies access without token")
    else:
        print(f"❌ Protected endpoint should return 403, got: {response.status_code}")
        return False
    
    # Test 5: Test OAuth accounts endpoint without token
    print("\n5️⃣ Testing OAuth accounts endpoint without token...")
    response = requests.get(f"{base_url}/api/v1/auth/oauth/accounts")
    if response.status_code == 403:
        print("✅ OAuth accounts endpoint correctly denies access without token")
    else:
        print(f"❌ OAuth accounts endpoint should return 403, got: {response.status_code}")
        return False
    
    print("\n✅ All OAuth endpoint tests passed!")
    print("\n📝 Manual testing required:")
    print("   1. Open browser and complete GitHub OAuth flow")
    print("   2. Open browser and complete Google OAuth flow")
    print("   3. Copy access tokens from browser responses")
    print("   4. Test protected endpoints with tokens")
    
    return True


def test_with_token(access_token: str):
    """Test protected endpoints with access token."""
    
    base_url = "http://127.0.0.1:8000"
    headers = {"Authorization": f"Bearer {access_token}"}
    
    print(f"\n🔐 Testing protected endpoints with token...")
    
    # Test 1: Get current user info
    print("\n1️⃣ Testing /auth/credential/me...")
    response = requests.get(f"{base_url}/api/v1/auth/credential/me", headers=headers)
    if response.status_code == 200:
        user_data = response.json()
        print(f"✅ Current user info retrieved")
        print(f"   ID: {user_data['id']}")
        print(f"   Username: {user_data['username']}")
        print(f"   Email: {user_data['email']}")
        print(f"   Status: {user_data['status']}")
    else:
        print(f"❌ Failed to get current user: {response.status_code}")
        if response.status_code == 401:
            print("   Token may be invalid or expired")
        return False
    
    # Test 2: Get OAuth accounts
    print("\n2️⃣ Testing /auth/oauth/accounts...")
    response = requests.get(f"{base_url}/api/v1/auth/oauth/accounts", headers=headers)
    if response.status_code == 200:
        oauth_data = response.json()
        print(f"✅ OAuth accounts retrieved")
        print(f"   Number of linked accounts: {len(oauth_data['oauth_accounts'])}")
        for account in oauth_data['oauth_accounts']:
            print(f"   - {account['provider']}: {account['provider_account_id']}")
    else:
        print(f"❌ Failed to get OAuth accounts: {response.status_code}")
        return False
    
    print("\n✅ All protected endpoint tests passed!")
    return True


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Test with provided token
        token = sys.argv[1]
        test_with_token(token)
    else:
        # Test OAuth endpoints
        test_oauth_endpoints()
