{".class": "MypyFile", "_fullname": "fastapi.security.api_key", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIKey": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.models.APIKey", "kind": "Gdef"}, "APIKeyBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.base.SecurityBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.api_key.APIKeyBase", "name": "APIKeyBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fastapi.security.api_key.APIKeyBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.api_key", "mro": ["fastapi.security.api_key.APIKeyBase", "fastapi.security.base.SecurityBase", "builtins.object"], "names": {".class": "SymbolTable", "check_api_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["api_key", "auto_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "fastapi.security.api_key.APIKeyBase.check_api_key", "name": "check_api_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["api_key", "auto_error"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_api_key of APIKeyBase", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "fastapi.security.api_key.APIKeyBase.check_api_key", "name": "check_api_key", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["api_key", "auto_error"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_api_key of APIKeyBase", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.api_key.APIKeyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.api_key.APIKeyBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "APIKeyCookie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.api_key.APIKeyBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.api_key.APIKeyCookie", "name": "APIKeyCookie", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fastapi.security.api_key.APIKeyCookie", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.api_key", "mro": ["fastapi.security.api_key.APIKeyCookie", "fastapi.security.api_key.APIKeyBase", "fastapi.security.base.SecurityBase", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "fastapi.security.api_key.APIKeyCookie.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["fastapi.security.api_key.APIKeyCookie", "starlette.requests.Request"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of APIKeyCookie", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "scheme_name", "description", "auto_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi.security.api_key.APIKeyCookie.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "scheme_name", "description", "auto_error"], "arg_types": ["fastapi.security.api_key.APIKeyCookie", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of APIKeyCookie", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.api_key.APIKeyCookie.auto_error", "name": "auto_error", "setter_type": null, "type": "builtins.bool"}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "fastapi.security.api_key.APIKeyCookie.model", "name": "model", "setter_type": null, "type": "fastapi.openapi.models.APIKey"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.api_key.APIKeyCookie.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.api_key.APIKeyCookie", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "APIKeyHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.api_key.APIKeyBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.api_key.APIKeyHeader", "name": "APIKeyHeader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fastapi.security.api_key.APIKeyHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.api_key", "mro": ["fastapi.security.api_key.APIKeyHeader", "fastapi.security.api_key.APIKeyBase", "fastapi.security.base.SecurityBase", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "fastapi.security.api_key.APIKeyHeader.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["fastapi.security.api_key.APIKeyHeader", "starlette.requests.Request"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of APIKeyHeader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "scheme_name", "description", "auto_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi.security.api_key.APIKeyHeader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "scheme_name", "description", "auto_error"], "arg_types": ["fastapi.security.api_key.APIKeyHeader", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of APIKeyHeader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.api_key.APIKeyHeader.auto_error", "name": "auto_error", "setter_type": null, "type": "builtins.bool"}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "fastapi.security.api_key.APIKeyHeader.model", "name": "model", "setter_type": null, "type": "fastapi.openapi.models.APIKey"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.api_key.APIKeyHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.api_key.APIKeyHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "APIKeyIn": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.models.APIKeyIn", "kind": "Gdef"}, "APIKeyQuery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.api_key.APIKeyBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.api_key.APIKeyQuery", "name": "APIKeyQuery", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fastapi.security.api_key.APIKeyQuery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.api_key", "mro": ["fastapi.security.api_key.APIKeyQuery", "fastapi.security.api_key.APIKeyBase", "fastapi.security.base.SecurityBase", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "fastapi.security.api_key.APIKeyQuery.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["fastapi.security.api_key.APIKeyQuery", "starlette.requests.Request"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of APIKeyQuery", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "scheme_name", "description", "auto_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi.security.api_key.APIKeyQuery.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "scheme_name", "description", "auto_error"], "arg_types": ["fastapi.security.api_key.APIKeyQuery", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of APIKeyQuery", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.api_key.APIKeyQuery.auto_error", "name": "auto_error", "setter_type": null, "type": "builtins.bool"}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "fastapi.security.api_key.APIKeyQuery.model", "name": "model", "setter_type": null, "type": "fastapi.openapi.models.APIKey"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.api_key.APIKeyQuery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.api_key.APIKeyQuery", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Doc", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "starlette.exceptions.HTTPException", "kind": "Gdef"}, "HTTP_403_FORBIDDEN": {".class": "SymbolTableNode", "cross_ref": "starlette.status.HTTP_403_FORBIDDEN", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "SecurityBase": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.base.SecurityBase", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.api_key.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.api_key.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.api_key.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.api_key.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.api_key.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.api_key.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "D:\\Workspace\\thoughtworks\\ai4se-workspace\\ai4se-mcp-hub\\.venv\\Lib\\site-packages\\fastapi\\security\\api_key.py"}