#!/usr/bin/env python3
"""
Script to seed OAuth provider configurations from environment variables.

This script creates OAuth provider configurations for GitHub and Google
using the existing environment variables, making them available through
the dynamic OAuth system.
"""

import os
import sys
import uuid
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig
from modules.oauth_provider.infrastructure.oauth_provider_repositories import (
    OAuthProviderRepositoryImpl,
)

# Load environment variables
load_dotenv()


def seed_oauth_providers():
    """Seed OAuth provider configurations from environment variables."""

    # Get database URL
    db_url = os.getenv("AI4SE_MCP_HUB_DB_URL")
    if not db_url:
        print("❌ Error: AI4SE_MCP_HUB_DB_URL environment variable not set")
        return False

    # Create engine and session
    engine = create_engine(db_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        oauth_provider_repo = OAuthProviderRepositoryImpl(session)

        providers_created = 0
        providers_updated = 0

        # GitHub OAuth Provider
        github_client_id = os.getenv("AI4SE_MCP_HUB_GITHUB_CLIENT_ID")
        github_client_secret = os.getenv("AI4SE_MCP_HUB_GITHUB_CLIENT_SECRET")

        if github_client_id and github_client_secret:
            existing_github = oauth_provider_repo.get_by_name("github")

            github_config = OAuthProviderConfig(
                id=existing_github.id if existing_github else uuid.uuid4(),
                name="github",
                display_name="GitHub",
                description="GitHub OAuth authentication provider",
                is_enabled=True,
                icon_url="https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
                client_id=github_client_id,
                client_secret=github_client_secret,
                authorize_url="https://github.com/login/oauth/authorize",
                token_url="https://github.com/login/oauth/access_token",
                user_info_url="https://api.github.com/user",
                scope="user:email",
                user_id_field="id",
                email_field="email",
                name_field="name",
                username_field="login",
                avatar_field="avatar_url",
                created_at=existing_github.created_at
                if existing_github
                else datetime.now(),
                updated_at=datetime.now(),
            )

            if existing_github:
                oauth_provider_repo.update(github_config)
                providers_updated += 1
                print("✅ Updated GitHub OAuth provider configuration")
            else:
                oauth_provider_repo.create(github_config)
                providers_created += 1
                print("✅ Created GitHub OAuth provider configuration")
        else:
            print(
                "⚠️  Skipping GitHub: Missing client ID or secret in environment variables"
            )

        # Google OAuth Provider
        google_client_id = os.getenv("AI4SE_MCP_HUB_GOOGLE_CLIENT_ID")
        google_client_secret = os.getenv("AI4SE_MCP_HUB_GOOGLE_CLIENT_SECRET")

        if google_client_id and google_client_secret:
            existing_google = oauth_provider_repo.get_by_name("google")

            google_config = OAuthProviderConfig(
                id=existing_google.id if existing_google else uuid.uuid4(),
                name="google",
                display_name="Google",
                description="Google OAuth authentication provider",
                is_enabled=True,
                icon_url="https://developers.google.com/identity/images/g-logo.png",
                client_id=google_client_id,
                client_secret=google_client_secret,
                authorize_url="https://accounts.google.com/o/oauth2/v2/auth",
                token_url="https://oauth2.googleapis.com/token",
                user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
                scope="openid email profile",
                user_id_field="id",
                email_field="email",
                name_field="name",
                username_field=None,  # Google doesn't have a username field
                avatar_field="picture",
                created_at=existing_google.created_at
                if existing_google
                else datetime.now(),
                updated_at=datetime.now(),
            )

            if existing_google:
                oauth_provider_repo.update(google_config)
                providers_updated += 1
                print("✅ Updated Google OAuth provider configuration")
            else:
                oauth_provider_repo.create(google_config)
                providers_created += 1
                print("✅ Created Google OAuth provider configuration")
        else:
            print(
                "⚠️  Skipping Google: Missing client ID or secret in environment variables"
            )

        # Commit changes
        session.commit()

        print("\n🎉 OAuth provider seeding completed!")
        print(f"   - Created: {providers_created} providers")
        print(f"   - Updated: {providers_updated} providers")

        return True


def main():
    """Main function."""
    print("🌱 Seeding OAuth provider configurations...")

    try:
        success = seed_oauth_providers()
        if success:
            print("\n✅ OAuth provider seeding completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ OAuth provider seeding failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during OAuth provider seeding: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
