{"data_mtime": 1750763436, "dep_lines": [6, 8, 9, 5, 11, 12, 1, 2, 3, 10, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.models", "fastapi.security.base", "fastapi.security.utils", "fastapi.exceptions", "starlette.requests", "starlette.status", "<PERSON><PERSON><PERSON><PERSON>", "base64", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "enum", "fastapi.openapi", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette"], "hash": "1f02281c8e66cc156817019e6450abee99de9d25", "id": "fastapi.security.http", "ignore_all": true, "interface_hash": "aaebb34f89e1eaa41f5b3a80fddf9e967e5ffeed", "mtime": 1750667923, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "D:\\Workspace\\thoughtworks\\ai4se-workspace\\ai4se-mcp-hub\\.venv\\Lib\\site-packages\\fastapi\\security\\http.py", "plugin_data": null, "size": 13606, "suppressed": [], "version_id": "1.16.1"}