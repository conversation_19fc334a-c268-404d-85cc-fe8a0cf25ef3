#!/usr/bin/env python3
"""Check OAuth test data in database."""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Load environment variables
load_dotenv()


def check_oauth_data():
    """Check users and oauth_accounts data in database."""
    
    # Get database URL
    db_url = os.getenv("AI4SE_MCP_HUB_DB_URL")
    if not db_url:
        print("❌ Error: AI4SE_MCP_HUB_DB_URL environment variable not set")
        return False
    
    try:
        # Create engine and session
        engine = create_engine(db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as session:
            print("📊 Checking OAuth test data...")
            
            # Check users
            result = session.execute(text("SELECT id, username, email FROM users"))
            users = result.fetchall()
            print(f"\n👥 Users ({len(users)}):")
            for user in users:
                print(f"   ID: {user[0]}")
                print(f"   Username: {user[1]}")
                print(f"   Email: {user[2]}")
                print("   ---")
            
            # Check oauth accounts
            result = session.execute(text("SELECT id, user_id, provider, provider_account_id FROM oauth_accounts"))
            oauth_accounts = result.fetchall()
            print(f"\n🔗 OAuth Accounts ({len(oauth_accounts)}):")
            for account in oauth_accounts:
                print(f"   ID: {account[0]}")
                print(f"   User ID: {account[1]}")
                print(f"   Provider: {account[2]}")
                print(f"   Provider Account ID: {account[3]}")
                print("   ---")
            
            print("✅ Data check completed!")
            return True
            
    except Exception as e:
        print(f"❌ Error checking data: {e}")
        return False


if __name__ == "__main__":
    check_oauth_data()
